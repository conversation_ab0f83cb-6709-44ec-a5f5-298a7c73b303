<template>
  <div class="dialog-tree-wrapper">
    <VxeTableTree
      ref="tableTree"
      :withPageHeader="false"
      :filter="true"
      :checkAll="false"
      :checkStrictly="true"
      :filter-source-data="filterSourceData"
      @checkbox-change="onCheckboxChange"
    />
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, useTemplateRef, nextTick } from 'vue'
  import { TreeNodeData, TreeNodeType } from '@/components/common/tableTree'
  import { VxeTableEvents } from 'vxe-table'
  import { speakInfo } from '@/utils/speak'

  const emit = defineEmits(['checkbox-change'])

  const props = withDefaults(
    defineProps<{
      showOnlyOrg?: boolean
      defaultCheckKeys?: string[]
    }>(),
    {
      showOnlyOrg: false,
      defaultCheckKeys: () => [],
    }
  )

  const tableTreeRef = useTemplateRef('tableTree')

  const onCheckboxChange: VxeTableEvents.CellDblclick<TreeNodeData> = row => {
    emit('checkbox-change', row)
  }
  const filterSourceData = (row: TreeNodeData) => {
    if (!props.showOnlyOrg) {
      return true
    }
    if (row.nodeType !== TreeNodeType.Org) {
      row.parentOrgId = ''
      return false
    }
    return true
  }

  const setDefaultCheckedNodes = async () => {
    if (!props.defaultCheckKeys || props.defaultCheckKeys.length === 0) {
      return
    }

    await nextTick()

    if (!tableTreeRef.value?.setCheckboxRowByRid) {
      return
    }

    props.defaultCheckKeys.forEach(dmrId => {
      const orgData = bfglob.gorgData.getDataByIndex(dmrId)
      if (orgData && orgData.rid) {
        tableTreeRef.value.setCheckboxRowByRid(orgData.rid, true)
      }
    })
  }

  onMounted(() => {
    setTimeout(() => {
      setDefaultCheckedNodes()
    }, 500) // 增加延迟时间确保树完全加载
  })
</script>

<style lang="scss">
  .dialog-tree-wrapper {
    height: 100%;
    width: 100%;
    font-family: 'AlibabaPuHuiTi2';

    .vxe-table .vxe-table--render-wrapper .vxe-column-content {
      color: #1ac8ed;
    }
  }
</style>
